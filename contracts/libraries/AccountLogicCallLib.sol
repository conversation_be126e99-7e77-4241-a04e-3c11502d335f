// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";
import "../interfaces/IAccountStorage.sol";
import {ContractManager} from "../ContractManager.sol";

/**
 * @dev AccountLogicCallLibライブラリ
 *      Accountのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */

library AccountLogicCallLib {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev バリデーション用のステータス値(アクティブ) */
    bytes32 private constant STATUS_ACTIVE = "active";
    /** @dev バリデーション用のステータス値(凍結) */
    bytes32 private constant STATUS_FROZEN = "frozen";
    /** @dev バリデーション用のステータス値(解約済) */
    bytes32 private constant STATUS_TERMINATED = "terminated";
    /** @dev バリデーション用のステータス値(強制償却済) */
    bytes32 private constant STATUS_FORCE_BURNED = "force_burned";

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 送金許可設定。
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkToken(IContractManager contractManager) external view {
        require(msg.sender == address(contractManager.token()), Error.GA0016_NOT_TOKEN_CONTRACT);
    }

    /**
     * @dev 送金許可設定。
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkTokenAndIBCToken(IContractManager contractManager) external view {
        require(
            (msg.sender == address(contractManager.token()) ||
                msg.sender == address(contractManager.ibcToken())),
            Error.GA0016_NOT_TOKEN_CONTRACT
        );
    }

    /**
     * @dev Issuerコントラクトからの呼び出しである事が条件。
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkIssuer(IContractManager contractManager) external view {
        require(msg.sender == address(contractManager.issuer()), Error.GA0008_NOT_ISSUER_CONTRACT);
    }

    /**
     * @dev addAccountの検証処理（統合版）
     * @param contractManager ContractManagerコントラクト参照
     * @param accountStorage AccountStorageコントラクト参照
     * @param accountId アカウントID
     */

    function checkAddAccountIsValid(
        IContractManager contractManager,
        IAccountStorage accountStorage,
        bytes32 accountId
    ) external view {
        checkValidator(contractManager);

        require(accountId != 0x00, Error.GE0105_ACCOUNT_ID_NOT_EXIST);

        // ID重複確認
        require(!accountStorage.getAccountIdExistence(accountId), Error.GE1010_ACCOUNT_ID_EXIST);
    }

    /**
     * @dev の検証処理
     * @param contractManager ContractManagerコントラクト参照
     */

    function checkValidator(IContractManager contractManager) internal view {
        require(
            msg.sender == address(contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
    }

    /**
     * @dev の検証処理
     * @param contractManager ContractManagerコントラクト参照
     */
    function checkValidatorAndFinance(IContractManager contractManager) internal view {
        require(
            msg.sender == address(contractManager.token()) ||
                msg.sender == address(contractManager.financialCheck()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
    }

    /**
     * @dev AccountのRoleを追加する。
     * @param contractManager ContractManagerコントラクト参照
     * @param accountEoa accountEoa
     */
    function checkAddAccountRoleIsValid(IContractManager contractManager, address accountEoa)
        external
        view
    {
        // 呼び出し元のコントラクトが、issuerコントラクトからの呼び出されているか確認
        require(msg.sender == address(contractManager.issuer()), Error.GA0008_NOT_ISSUER_CONTRACT);
        // accountEoaが有効な値であるか確認
        require(accountEoa != address(0), Error.RV0007_ACCOUNT_INVALID_VAL);
    }

    /**
     * @dev AccountID存在確認 本体(内部関数)。
     * @param accountStorage AccountStorageコントラクト参照
     * @param accountId accountId
     */
    function checkHasAccount(IAccountStorage accountStorage, bytes32 accountId) public view {
        (bool success, string memory err) = hasAccount(accountStorage, accountId);
        require(success, err);
    }

    function hasAccount(IAccountStorage accountStorage, bytes32 accountId)
        internal
        view
        returns (bool success, string memory err)
    {
        if (accountId == 0x00) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }
        if (!accountStorage.getAccountIdExistence(accountId)) {
            return (false, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev Accountの有効性を更新する(凍結 or アクティブ)
     * @param contractManager ContractManagerコントラクト参照
     * @param accountStorage AccountStorageコントラクト参照
     * @param msgSender msgSender
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountStatus アカウントステータス
     */
    function checkAccountStatusIsValid(
        IAccountStorage accountStorage,
        IContractManager contractManager,
        address msgSender,
        bytes32 accountId,
        bytes32 accountStatus
    ) external view {
        // 呼び出し元のコントラクトが、issuerコントラクトからの呼び出されているか確認
        require(msgSender == address(contractManager.issuer()), Error.GA0008_NOT_ISSUER_CONTRACT);
        //Account存在確認
        {
            (bool success, string memory errTmp) = contractManager.account().hasAccount(accountId);
            require(success, errTmp);
        }
        bytes32 currentStatus = accountStorage.getAccountStatus(accountId);

        require(
            accountStatus == STATUS_ACTIVE || accountStatus == STATUS_FROZEN,
            Error.RV0007_ACCOUNT_INVALID_VAL
        );

        if (accountStatus == STATUS_ACTIVE) {
            require(
                (currentStatus == STATUS_FROZEN || currentStatus == STATUS_FORCE_BURNED),
                Error.ACCOUNT_NOT_FROZEN_OR_FORCE_BURNED
            );
        }
        if (accountStatus == STATUS_FROZEN) {
            require(currentStatus == STATUS_ACTIVE, Error.GE2005_ACCOUNT_DISABLED);
        }
    }

    /**
     * @dev アカウントのステータスを解約済みに更新する
     * @param contractManager ContractManagerコントラクト参照
     * @param accountId マッピングのキーとなるアカウントID
     */
    function checkTerminatedIsValid(IContractManager contractManager, bytes32 accountId)
        external
        view
    {
        // 呼び出し元のコントラクトのmsg.senderがvalidatorコントラクトからの呼び出しである事が条件
        checkValidator(contractManager);

        //Account存在確認
        (bool success, string memory err) = contractManager.account().hasAccount(accountId);
        require(success, err);

        // Accountの残高を確認
        (AccountDataWithoutZoneId memory accountData, ) = contractManager.account().getAccount(
            accountId
        );
        require(accountData.balance == 0, Error.ACCOUNT_BALANCE_NOT_ZERO);
    }

    /**
     * @dev アカウントの全ゾーン残高を取得する
     * @notice FinZoneの残高を最初に、その後BizZoneの残高をZoneID昇順で返す
     * @notice emitAfterBalanceから呼ばれる関数（権限チェックなし）
     * @param contractManager ContractManager リファレンス
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId 残高を取得するアカウントのID
     * @return allBalance FinZoneとBizZoneの残高配列
     *         - 配列の最初の要素: FinZoneの残高（現在のゾーンの残高）
     *         - 配列の2番目以降: BizZoneの残高（ZoneID昇順でソート済み）
     */
    function getAllBalance(
        IAccountStorage accountStorage,
        IContractManager contractManager,
        bytes32 accountId
    ) public view returns (AllBalanceData[] memory allBalance, uint256 totalBalance) {
        // ゾーンの取得
        (uint16 zoneId, , ) = contractManager.provider().getZone();
        // ゾーンの取得
        ZoneData[] memory zones = contractManager.account().getZoneByAccountId(accountId);

        // 配列の初期化（FinZone + BizZones）
        allBalance = new AllBalanceData[](zones.length + 1);

        // FinZoneの残高
        allBalance[0].zoneId = zoneId;
        allBalance[0].balance = accountStorage.getAccountBalance(accountId);
        totalBalance = accountStorage.getAccountBalance(accountId);

        // BizZoneデータを昇順で配置（3001-3999の範囲）
        uint256 insertIndex = 1;
        for (uint16 targetZoneId = 3001; targetZoneId <= 3999; targetZoneId++) {
            for (uint256 i = 0; i < zones.length; i++) {
                if (zones[i].zoneId == targetZoneId) {
                    allBalance[insertIndex].zoneId = targetZoneId;
                    BusinessZoneAccountData memory bizData = contractManager
                        .businessZoneAccount()
                        .getBusinessZoneAccount(targetZoneId, accountId);
                    allBalance[insertIndex].balance = bizData.balance;
                    insertIndex++;
                    totalBalance += bizData.balance;
                    break;
                }
            }
            if (insertIndex > zones.length) break;
        }

        return (allBalance, totalBalance);
    }

    /**
     * @dev アカウントに紐づくバリデータIDを取得する
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId アカウントID
     * @return validatorId バリデータID
     * @return err エラー
     */
    function getValidatorIdByAccountId(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (bytes32 validatorId, string memory err)
    {
        bool success;
        (success, err) = hasAccount(accountStorage, accountId);
        if (!success) {
            return (0x00, err);
        }

        return (accountStorage.getValidatorId(accountId), "");
    }

    /**
     * @dev 発行後の残高。
     * @param accountId 発行者ID
     * @return balance 発行後の残高
     */
    function getAccountBalance(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (uint256 balance)
    {
        return accountStorage.getAccountBalance(accountId);
    }

    /**
     * @dev アカウントがアクティブかどうかを取得する
     *
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountStorage AccountStorage 契約リファレンス
     * @return success
     * @return err
     */
    function isActivated(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (bool success, string memory err)
    {
        if (accountStorage.getAccountStatus(accountId) == STATUS_ACTIVE) {
            return (true, "");
        } else {
            return (false, Error.GE2005_ACCOUNT_DISABLED);
        }
    }

    /**
     * @dev アカウントが解約状態かどうかを取得する
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     * @return terminated
     */
    function isTerminated(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (bool terminated)
    {
        if (accountStorage.getAccountStatus(accountId) == STATUS_TERMINATED) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @dev アカウント情報を取得する
     * @param accountId マッピングのキーとなるアカウントID
     * @param accountStorage AccountStorage 契約リファレンス
     * @return accountDataWithoutZoneId アカウントデータ(zoneIdなし)
     * @return err エラーメッセージ
     */
    function getAccountDataWithoutZoneId(
        IAccountStorage accountStorage,
        bytes32 accountId,
        bool success,
        string memory errTmp
    )
        external
        view
        returns (AccountDataWithoutZoneId memory accountDataWithoutZoneId, string memory err)
    {
        if (!success) {
            return (accountDataWithoutZoneId, errTmp);
        }
        accountDataWithoutZoneId = accountStorage.getAccountDataWithoutZoneId(accountId);
        return (accountDataWithoutZoneId, "");
    }

    /**
     * @dev Accountの全情報を返す。
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     * @return accountDataAll アカウントデータ
     */
    function getAccountDataAll(
        IAccountStorage accountStorage,
        IContractManager contractManager,
        bytes32 accountId
    ) external view returns (AccountDataAll memory accountDataAll) {
        // アカウントデータとフィナンシャルゾーンアカウントデータの取得
        AccountData memory accountData = accountStorage.getAccountData(accountId);
        // フィナンシャルゾーンアカウントデータの取得
        (FinancialZoneAccountData memory financialZoneAccountData, ) = contractManager
            .financialZoneAccount()
            .getAccountLimitData(accountId);
        // ゾーン情報の取得 TODO: エラーリターンの考慮をする
        (uint16 zoneId, string memory zoneName, ) = contractManager.provider().getZone();

        // ビジネスゾーンアカウントデータの準備
        // TODO: ループが無制限になることを防ぐため、zoneIdsの取得上限に1000件などのlimitを設ける
        uint16[] memory zoneIds = accountData.zoneIds;
        BusinessZoneAccountDataWithZoneId[]
            memory businessZoneAccountDataWithZoneIds = new BusinessZoneAccountDataWithZoneId[](
                zoneIds.length
            );
        // 各ビジネスゾーンアカウントデータの取得
        for (uint256 i = 0; i < zoneIds.length; i++) {
            BusinessZoneAccountData memory bizZoneData = contractManager
                .businessZoneAccount()
                .getBusinessZoneAccount(zoneIds[i], accountId);
            businessZoneAccountDataWithZoneIds[i] = _packZoneAccountData(
                contractManager,
                zoneIds[i],
                bizZoneData
            );
        }
        return
            AccountDataAll({
                accountName: accountData.accountName,
                accountStatus: accountData.accountStatus,
                balance: accountData.balance,
                reasonCode: accountData.reasonCode,
                zoneId: zoneId,
                zoneName: zoneName,
                appliedAt: accountData.appliedAt,
                registeredAt: accountData.registeredAt,
                terminatingAt: accountData.terminatingAt,
                terminatedAt: accountData.terminatedAt,
                mintLimit: financialZoneAccountData.mintLimit,
                burnLimit: financialZoneAccountData.burnLimit,
                chargeLimit: financialZoneAccountData.chargeLimit,
                dischargeLimit: financialZoneAccountData.dischargeLimit,
                transferLimit: financialZoneAccountData.transferLimit,
                cumulativeLimit: financialZoneAccountData.cumulativeLimit,
                cumulativeAmount: financialZoneAccountData.cumulativeAmount,
                cumulativeDate: financialZoneAccountData.cumulativeDate,
                cumulativeTransactionLimits: financialZoneAccountData.cumulativeTransactionLimits,
                businessZoneAccounts: businessZoneAccountDataWithZoneIds
            });
    }

    function _packZoneAccountData(
        IContractManager contractManager,
        uint16 zoneId,
        BusinessZoneAccountData memory data
    ) private view returns (BusinessZoneAccountDataWithZoneId memory) {
        return
            BusinessZoneAccountDataWithZoneId({
                accountName: data.accountName,
                zoneId: zoneId,
                zoneName: contractManager.provider().getZoneName(zoneId),
                balance: data.balance,
                accountStatus: data.accountStatus,
                appliedAt: data.appliedAt,
                registeredAt: data.registeredAt,
                terminatingAt: data.terminatingAt,
                terminatedAt: data.terminatedAt
            });
    }

    /**
     * @dev IndexよりAccountIDを取得する。
     *
     * @param index index
     * @param accountStorage AccountStorage 契約リファレンス
     * @return accountId accountId
     * @return err エラーメッセージ
     */
    function getAccountId(IAccountStorage accountStorage, uint256 index)
        external
        view
        returns (bytes32 accountId, string memory err)
    {
        return accountStorage.getAccountId(index);
    }

    /**
     * @dev アカウントの許可額を取得する
     *
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     * @param index 許可対象のアカウントID
     * @return allowance
     * @return approvedAt
     */
    function getAllowance(
        IAccountStorage accountStorage,
        bytes32 accountId,
        bytes32 index
    ) external view returns (uint256 allowance, uint256 approvedAt) {
        return accountStorage.getAllowance(accountId, index);
    }

    /**
     * @dev 送金許可一覧照会 TODO:Core APIとのマッピング時に作成
     * @param accountStorage AccountStorage 契約リファレンス
     * @param ownerId 送金許可元ID
     * @param offset オフセット
     * @param limit リミット
     * @return approvalData 送金許可設定一覧
     * @return totalCount 総数
     * @return err エラーメッセージ
     */
    function getAllowanceList(
        IAccountStorage accountStorage,
        bytes32 ownerId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        )
    {
        return accountStorage.getAllowanceList(ownerId, offset, limit);
    }

    /**
     * @dev Accountの数を返却する。
     * @param accountStorage AccountStorage 契約リファレンス
     * @param count accountの数
     */
    function getAccountCount(IAccountStorage accountStorage) external view returns (uint256 count) {
        return accountStorage.getAccountCount();
    }

    /**
     * @dev アカウントに連携済みのzoneIdの取得
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     * @return zoneIdList アカウントに連携済みのzoneIdのリスト
     */
    function getAccountZoneIdList(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (uint16[] memory zoneIdList)
    {
        return accountStorage.getAccountZoneIdList(accountId);
    }

    /**
     * @dev アカウントが凍結状態かどうかを取得する
     *
     * @param accountStorage AccountStorage 契約リファレンス
     * @param accountId マッピングのキーとなるアカウントID
     */
    function isFrozen(IAccountStorage accountStorage, bytes32 accountId)
        external
        view
        returns (bool frozen)
    {
        if (accountStorage.getAccountStatus(accountId) == STATUS_FROZEN) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @dev limitとoffsetで指定したAccountsを一括取得する
     * @param accountStorage AccountStorage 契約リファレンス
     */
    function getAccountAll(IAccountStorage accountStorage, uint256 index)
        external
        view
        returns (AccountsAll memory account)
    {
        return accountStorage.getAccountAll(index);
    }
}
